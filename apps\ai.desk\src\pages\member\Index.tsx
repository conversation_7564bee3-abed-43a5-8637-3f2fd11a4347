import PageHeader from "@/components/member/PageHeader";
import { CnaButton } from "@code.8cent/react/components";
import {
    SimpleGrid,
    Image,
    Card,
    Text,
    Group,
    ScrollArea,
} from "@mantine/core";
import { ArrowRight } from "@phosphor-icons/react";
import { useNavigate } from "react-router-dom";

const Items = [
    {
        title: "业务管理",
        subTitle: "帮助您跟进项目进度，在服务企业中获得收益分成",
        icon: "/images/station/index-icon-case.svg",
        link: "/member/business",
    },
    {
        title: "管理合伙人",
        subTitle: "晋级为管理合伙人后，推荐更多合伙人加盟获得津贴",
        icon: "/images/station/index-icon-user.svg",
        link: "/member/associates",
    },
    {
        title: "出入款管理",
        subTitle: "查看付款记录和接收业务服务费，帮助合伙人掌握财务状况",
        icon: "/images/station/index-icon-money.svg",
        link: "/member/billing",
    },
    {
        title: "资料文档",
        subTitle: "集中存储企业文档与参考资料，支持合伙人高效工作",
        icon: "/images/station/index-icon-file.svg",
        link: "/member/document",
    },
    {
        title: "个人简介",
        subTitle: "合伙人个人资料 ，包括业务案例、资格证书、牌照认证",
        icon: "/images/station/index-icon-list.svg",
        link: "/member/summary",
    },
];

const ItemCard = ({ item }: { item: (typeof Items)[number] }) => {
    const navigate = useNavigate();

    return (
        <div
            className="tw-relative tw-cursor-pointer"
            onClick={() => {
                navigate(item.link, { replace: true });
            }}
        >
            <Card
                radius="lg"
                p="xl"
                className="group tw-mt-[130px] tw-w-full tw-cursor-pointer tw-max-h-[600px] hover:tw-bg-[linear-gradient(to_bottom,#425097_0%,#3141A6_74%,#5377D4_100%)]"
            >
                <Group
                    justify="flex-start"
                    mb="md"
                    mt={160}
                >
                    <CnaButton
                        variant="transparent"
                        size="sm"
                        className="tw-bg-[#EAEEFF] group-hover:!tw-bg-white"
                        radius="lg"
                    >
                        <ArrowRight
                            color="#19288F"
                            weight="bold"
                        />
                    </CnaButton>
                </Group>

                <Text
                    size="lg"
                    fw={700}
                    className="tw-text-[#292F53] group-hover:!tw-text-white"
                >
                    {item.title}
                </Text>
                <Text
                    c="dimmed"
                    size="sm"
                    mb="md"
                >
                    {item.subTitle}
                </Text>
            </Card>
            <Image
                src={item.icon}
                className="tw-absolute tw-top-0 tw-left-1/2 tw-transform tw--translate-x-1/2"
            />
        </div>
    );
};

const MemberIndex = () => {
    return (
        <div>
            <PageHeader />
            <ScrollArea className="tw-h-[calc(100vh-250px)]">
                <SimpleGrid cols={{ base: 1, sm: 2, md: 3, lg: 4, xl: 5 }}>
                    {Items.map((item) => (
                        <ItemCard
                            key={item.title}
                            item={item}
                        />
                    ))}
                </SimpleGrid>
            </ScrollArea>
        </div>
    );
};

export default MemberIndex;
